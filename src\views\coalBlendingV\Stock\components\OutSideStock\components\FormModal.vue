<template>
    <div class="container">
        <SnModal v-model="_value" :cancel="cancel" :fullscreen.sync="fullscreen" :ok="ok" :showFooter="!isViewStatus"
                 :title="title"
                 class="modal" v-bind="$attrs" width="70%">
            <el-form ref="form" :disabled="isViewStatus" :model="form" :rules="rules" labelPosition="top"
                     labelWidth="100px"
                     style="height: 100%">
                <SnFormCard title="基本信息">
                    <VhTable ref="excelTable" v-if="getRangeCols.length" :afterChange="afterChange"
                             :beforePaste="beforePaste" :list="form.data"
                             class="table-content"
                             :nestedHeaders="[
                                 [
                                  { label: '基础信息', colspan: 43},
                                  { label: '精煤指标', colspan: getRangeCols.length },
                                 ]
                             ]"
                             @init="setTableSetting">
                        <VHColumn data="updateDate" title="更新日期" formRequire type="text" width="100">
                            <template #editors="slotProps">
                                <VHDate :slotProps="slotProps" format="yyyy-MM-dd" valueFormat="yyyy-MM-dd"/>
                            </template>
                        </VHColumn>
                        <VHColumn data="coalCategoryName" title="煤种名称" type="autocomplete" width="120">
                            <template #default="{ value }">
                                <span class="t-ellipsis">{{ value }}</span>
                            </template>
                        </VHColumn>
                        <VHColumn data="coalName" title="品名" formRequire type="autocomplete" width="120">
                            <template #default="{ value }">
                                <span class="t-ellipsis">{{ value }}</span>
                            </template>
                        </VHColumn>

                        <!--                        <VHColumn data="coalSource" title="来源" type="text" width="100">-->
                        <!--                            <template #default="{ value }">-->
                        <!--                                <SnDictTag :value="value" class="t-ellipsis" type="b_coal_source_type"></SnDictTag>-->
                        <!--                            </template>-->
                        <!--                            <template #editors="slotProps">-->
                        <!--                                <VHSelect :list="coalSourceList" :props="{-->
                        <!--                                        value: 'value',-->
                        <!--                                        label: 'name',-->
                        <!--                                        key: 'value'-->
                        <!--                                    }" :slotProps="slotProps"/>-->
                        <!--                            </template>-->
                        <!--                        </VHColumn>-->

                        <!-- <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanMt" data="stock" title="库存" type="numeric"
                                  width="90"/> -->
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanMt" data="mt" formRequire title="水分Mt%"
                                  type="numeric"
                                  width="90"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanAd" data="ad" formRequire title="灰分Ad%"
                                  type="numeric"
                                  width="70"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanStd" data="std" formRequire title="硫分St,d%"
                                  type="numeric"
                                  width="80"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.cleanVdaf" data="vdaf" formRequire
                                  title="挥发分Vdaf%"
                                  type="numeric" width="100"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.procG" data="g" formRequire title="粘结G"
                                  type="numeric"
                                  width="70"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.procY" data="y" title="Y/mm"
                                  type="numeric"
                                  width="70"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.procY" data="x" title="X/mm" type="numeric"
                                  width="70"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.default3" data="macR0" title="反射率" type="numeric"
                                  width="70"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.default3" data="macS" title="标准差" type="numeric"
                                  width="70"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.default" data="cri" title="CRI%" type="numeric"
                                  width="70"/>
                        <VHColumn :numericFormat="INDEX_NUM_FORMAT.default" data="csr" title="CSR%" type="numeric"
                                  width="70"/>
                        <!-- <VHColumn data="activePriceCoalPriceNoTax" title="不含税煤价" type="numeric"
                                  width="100">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn> -->
                        <VHColumn data="activePriceTransportPriceNoTax" title="运费" type="numeric" width="100">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn>
                        <VHColumn data="activePriceCoalPriceWithTax" title="含税煤价" type="numeric" width="100">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn>
                        <!-- <VHColumn data="activePriceTransportPriceNoTax" title="不含税运费" type="numeric" width="100">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn> -->

                        <!-- <VHColumn data="activePriceTransportCostP1" title="路耗%" type="numeric"
                                  width="80">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn> -->

                        


                        <!-- <VHColumn data="activePriceFactoryPriceNoTax" readOnly title="不含税到厂煤价" type="numeric"
                                  width="120">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn> -->

                        <!-- <VHColumn data="reduceMtStandard" title="折水标准" :numericFormat="INDEX_NUM_FORMAT.default0"
                                  type="numeric" width="100"/> -->

                        <!-- <VHColumn data="reduceMtFactoryPriceNoTax" readOnly title="折水不含税进厂价"
                                  :numericFormat="INDEX_NUM_FORMAT.default2" type="numeric" width="120"/> -->
                        <VHColumn data="coalBlendingCost" title="配煤成本"
                                  :numericFormat="INDEX_NUM_FORMAT.default2" type="numeric" width="100"/>
                        <VHColumn data="location" title="产地" type="text" width="100"/>
                        <VHColumn data="remarks" title="备注" type="text" width="100"/>
                        <VHColumn data="procMf" title="MF/ddpm" :numericFormat="INDEX_NUM_FORMAT.default" type="numeric"
                                  width="100"/>
                        <VHColumn data="procB" title="奥亚膨胀b%" :numericFormat="INDEX_NUM_FORMAT.default"
                                  type="numeric" width="100"/>
                        <VHColumn data="procA" title="奥亚收缩a%" :numericFormat="INDEX_NUM_FORMAT.default"
                                  type="numeric" width="100"/>


                        <!-- <VHColumn data="vd" title="Vd%" readOnly :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/> -->
                        <VHColumn data="siO2" title="SiO2%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="al2O3" title="Al2O3%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="fe2O3" title="Fe2O3%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="caO" title="CaO%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="k2O" title="K2O%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="na2O" title="Na2O%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="mgO" title="MgO%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="tiO2" title="TiO2%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>
                        <VHColumn data="mnO2" title="MnO2%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>


                        <VHColumn data="p2O5" title="P205%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>

                        <VHColumn data="so3" title="SO3%" :numericFormat="INDEX_NUM_FORMAT.default2"
                                  type="numeric" width="80"/>


                        <VHColumn data="mci" title="MCI%" readOnly :numericFormat="INDEX_NUM_FORMAT.default"
                                  type="numeric"
                                  width="100">
                            <template #default="{ value }">
                                <span>{{ getShowValue(value, 2, 'ROUND_HALF_UP') }}</span>
                            </template>
                        </VHColumn>


                        <VHColumn data="activeInertiaRatio" title="活惰比" :numericFormat="INDEX_NUM_FORMAT.default"
                                  type="numeric" width="100"/>


                        <VHColumn :data="item.data" v-for="item in getRangeCols" :key="item.data" :title="item.title"
                                  :numericFormat="INDEX_NUM_FORMAT.default"
                                  type="numeric" width="70"/>
                                  <VHColumn data="categoryType" title="分类" type="text"
                                  width="100">
                            <template #default="{ value }">
                                <span class="t-ellipsis">{{ value }}</span>
                            </template>

                            <template #editors="slotProps">
                                <VHSelect :list="categoryTypeList" :props="{
                                        value: 'value',
                                        label: 'name',
                                        key: 'value'
                                    }" :slotProps="slotProps"/>
                            </template>
                        </VHColumn>
                    </VhTable>
                </SnFormCard>
            </el-form>
        </SnModal>
    </div>
</template>

<script>
import TipModal from '@/utils/modal'
import {deepClone, FetchData} from '@/utils'
import {VhTable, VHColumn, VHDate, VHSelect} from '@/components/ExcelTable'
import {INDEX_NUM_FORMAT} from '@/const'
import {getDate} from '@/utils/dateUtils'
import CalcUtils from '@/utils/calcUtils'
import {getCoalNameList, getRangeListApi} from "@/api/coal";
import {getDictDatas} from '@/utils/dict'
import {isDef} from "@/utils/is";

export default {
    inheritAttrs: false,
    components: {
        VHDate,
        VHSelect,
        VhTable,
        VHColumn
    },
    data() {
        return {
            form: {},
            rules: {},
            fullscreen: true,
            rangeList: [],
            categoryTypeList: [
                {value: '精煤', name: '精煤'},
                {value: '原煤', name: '原煤'},
            ],
            coalNameList: [],
            coalSourceList: getDictDatas('b_coal_source_type')
        }
    },
    computed: {
        getRangeCols() {
            return this.getFormatRangeList(this.rangeList)
        },
        INDEX_NUM_FORMAT() {
            return INDEX_NUM_FORMAT
        },
        getOptMap() {
            return {
                create: '新增',
                update: '修改',
                view: '查看'
            }
        },
        _value: {
            set(val) {
                this.$emit('input', val)
            },
            get() {
                return this.value
            }
        },
        title() {
            return this.getOptMap[this.optName]
        },
        isViewStatus() {
            return this.optName === 'view'
        },
        isUpdateStatus() {
            return this.optName === 'update'
        },
        isCreateStatus() {
            return this.optName === 'create'
        }
    },
    props: {
        useApi: {
            type: Boolean,
            default: false
        },
        getApi: {
            type: Function
        },
        optName: {
            type: String,
            require: true
        },
        value: {
            type: Boolean,
            require: true
        },

        record: {
            type: Object,
            default() {
                return {}
            }
        },
        model: {
            type: Object,
            default() {
                return {}
            }
        },
        categoryNamesList: {
            type: Array,
            default() {
                return []
            }
        },
        coalSource: {
            type: String,
        }
    },
    watch: {
        record: {
            async handler(value) {
                await this.$nextTick()
                if (this.isCreateStatus) {
                    this.form = {
                        ...value,
                        data: Array(15).fill().map(() => ({
                            activePriceTransportCostP1: 0.5,
                            updateDate: getDate(new Date()),
                            categoryType: '精煤',
                            reduceMtStandard: 8
                        }))
                    }
                    return true
                }

                this.form = this.getFormData(value)


            },
            immediate: true
        }
    },
    created() {

        this.getDataList()
    },
    methods: {
        beforePaste(data, coords) {
            const cols = this.$refs.excelTable.columns
            const dateIndex = cols?.find((v) => v.data === 'updateDate')?.columnIndex
            const [rowItem] = coords
            if (rowItem.startCol === dateIndex) {
                data.forEach((valData) => {
                    valData[dateIndex] = getDate(valData[dateIndex]) || ''
                })
            }
        },
        getShowValue: CalcUtils.getShowValue,
        afterChange(changes, source, instance) {
            if (source === 'source') return
            // // eslint-disable-next-line no-unused-expressions
            changes?.forEach(([row, prop, oldValue, newValue]) => {
                const v = instance.getSourceDataAtRow(row)
                const set = (prop, value) => {
                    instance.setDataAtRowProp(row, prop, value)
                }
                const isEmpty = (v) => ['', null, undefined].includes(v)
                const taxPriceEffect = () => set('activePriceFactoryPriceNoTax', CalcUtils.calculateTaxPrice(v.activePriceCoalPriceWithTax, v.activePriceTransportPriceNoTax, v.activePriceTransportCostP1))
                const noTaxPriceEffect = () => set('activePriceFactoryPriceNoTax', CalcUtils.calculateNoTaxPrice(v.activePriceCoalPriceNoTax, v.activePriceTransportPriceNoTax, v.activePriceTransportCostP1))

                if (['activePriceCoalPriceNoTax', 'activePriceCoalPriceWithTax', 'activePriceTransportPriceNoTax', 'activePriceTransportCostP1'].includes(prop)) {
                    if (!isEmpty(v.activePriceCoalPriceWithTax) && !isEmpty(v.activePriceCoalPriceNoTax)) { // 如果都不位空则默认使用不含税价格算
                        noTaxPriceEffect()
                    } else if (isEmpty(v.activePriceCoalPriceWithTax)) {
                        noTaxPriceEffect()
                    } else if (isEmpty(v.activePriceCoalPriceNoTax)) {
                        taxPriceEffect()
                    }
                }


                // 折水不含税进厂价
                if (['mt', 'reduceMtStandard', 'activePriceFactoryPriceNoTax',].includes(prop)) {
                    set('reduceMtFactoryPriceNoTax', CalcUtils.calculateRemoveWaterNoTaxPrice(v.mt, v.reduceMtStandard, v.activePriceFactoryPriceNoTax))
                }
                if (['reduceMtFactoryPriceNoTax'].includes(prop)) {
                    set('coalBlendingCost', v.reduceMtFactoryPriceNoTax)
                }


                if (['ad', 'vdaf'].includes(prop)) {
                    set('vd', CalcUtils.calculateVd(v))
                }

                // mci
                if (['ad', 'vdaf', 'siO2', 'al2O3', 'fe2O3', 'caO', 'k2O', 'na2O', 'mgO', 'tiO2', 'mnO2'].includes(prop)) {
                    set('mci', CalcUtils.calculateMCI(v))
                }
            })
        },
        async setTableSetting() {
            const categoryNamesList = this.categoryNamesList
            const settingConfig = () => {
                if (this.isUpdateStatus) return {
                    contextMenu: {
                        items: {
                            remove_row: {name: '删除行'},
                        }
                    }
                }
                if (!this.isCreateStatus) return {}
                return {
                    contextMenu: {
                        items: {
                            row_above: {name: '向上插入一行'},
                            row_below: {name: '向下插入一行'},
                            remove_row: {name: '删除行'},
                            clear_custom: {
                                name: '清空所有单元格数据',
                                callback() {
                                    this.clear()
                                }
                            }
                        }
                    }
                }
            }
            const instance = this.$refs.excelTable.getHotInstance()
            const cols = this.$refs.excelTable.columns
            instance.updateSettings({
                ...settingConfig(),
                cells(row, col, prop) {
                    const cellProperties = {
                        className: 't-ellipsis'
                    }

                    if (cols[col].data === 'coalCategoryName') {
                        cellProperties.source = categoryNamesList
                    }

                    return cellProperties
                }
            })
        },

        async getDataList() {
            try {
                const {data} = await getRangeListApi()
                this.rangeList = data
            } catch (e) {
                this.rangeList = []
            }
        },


        getFormatRangeList(list) {
            return list.map(v => {
                const dataKey = v.rangeName.replaceAll('.', 'dot')
                return {
                    ...v,
                    data: dataKey,
                    title: v.rangeName,
                }
            })
        },

        getFormData(form) {
            const {...rest} = form
            return {
                ...rest,
                data: rest.data.map((row) => {
                    const rangeObjValues = this.getFormatRangeList(row.rangeValues).reduce((res, cur) => {
                        const {proportion, data} = cur
                        return {...res, [data]: proportion,}
                    }, {})
                    return {
                        ...row,
                        updateDate: getDate(row.updateDate || row.date),
                        ...rangeObjValues,
                        coalSource: row.coalSource,
                    }
                })
            }
        },

        getSubmitForm() {
            const submitForm = deepClone(this.form)
            const {...rest} = submitForm
            const {getData, getColumns, getFormatEditData} = this.$refs.excelTable
            const data = getFormatEditData(getData(), getColumns(), (item, index) => {

                if (Object.keys(item).length <= 4) {
                    if (isDef(item.activePriceTransportCostP1)) {
                        delete item.activePriceTransportCostP1
                    }
                    if (isDef(item.updateDate)) {
                        delete item.updateDate
                    }
                    if (isDef(item.categoryType)) {
                        delete item.categoryType
                    }
                    if (isDef(item.reduceMtStandard)) {
                        delete item.reduceMtStandard
                    }
                }


            })
            const formatData = (data = []) => {
                return data.map((v) => {
                    const rockList = this.rangeList.map(rockItem => {
                        const key = rockItem.rangeName.replaceAll('.', 'dot')
                        return {
                            rangeName: rockItem.rangeName,
                            proportion: v[key],
                            sort: rockItem.sort,
                        }
                    })
                    return {
                        ...v,
                        coalSource: v.coalSource || this.coalSource,
                        rockList
                    }
                })
            }

            return {
                ...rest,
                data: formatData(data)
            }
        },

        async cancel() {
            this.form = {}
            this.$refs['form'].resetFields()
            return false
        },

        async ok() {
            await this.$refs['form'].validate()
            const status = await this.$refs['excelTable'].validate()
            if (!status) return
            const apiFunc = this.isCreateStatus ? this.model.add : this.model.update
            const resp = await apiFunc(this.getSubmitForm())
            if (resp) {
                TipModal.msgSuccess(`${this.title}成功`)
                this.$emit('ok')
                return this.cancel()
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  height: 100%;
}
</style>
