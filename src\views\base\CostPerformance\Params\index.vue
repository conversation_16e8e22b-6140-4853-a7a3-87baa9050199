<template>
  <div class="app-container">
    <div class="common--page">
      <div class="search-form-wrapper">
        <SnSimpleSelect v-model="coalCategoryId" v-bind="{
            list: coalCategoryList,
            type: 'radiobutton',
            props: coalCategoryProps
          }"/>
      </div>

      <div class="common--page-content">
        <SnFormCard titlePrefixIcon="">
          <template #title>
            <i class="el-icon-s-tools"></i>
            <span style="margin:0 20px 0 7px">基准值&升贴配置</span>
            <el-button :loading="loading" type="primary" @click.stop="handleUpdate">更新</el-button>
          </template>
          <el-table :data="listBase" v-bind="getTableConfig">
            <el-table-column v-for="col in tableColsBase" :key="col.prop" :label="col.label"
                             :prop="col.prop" align="center">
              <template #default="{row}">
                <SnSimpleSelect v-if="col.prop === 'coalName'" v-model="row.coalName"
                                :list="coalNameList"
                                :placeholder="`请选择${col.label}`" :props="coalNameProps"
                                @select="handleNameChange(col, row, ...arguments)">
                  <template #default="item">
                    <div style="display:flex;justify-content:space-between;">
                      <span>{{ item[coalNameProps.label] }}</span>
                      <span v-if="item.type==='zy'" style="color: #8492a6;font-size: 12px;margin-left: 10px;">
                        内部
                      </span>
                      <span v-if="item.type==='wb'" style="color: #8492a6;font-size: 12px;margin-left: 10px;">
                        外部
                      </span>
                      <span v-if="item.type==='zsmj'" style="color: #8492a6;font-size: 12px;margin-left: 10px;">
                        掌上煤焦
                      </span>
                    </div>
                  </template>
                </SnSimpleSelect>
                <el-input v-else v-model="row[col.prop]" v-focus :placeholder="`请输入${col.label}`"
                          clearable></el-input>
              </template>
            </el-table-column>
          </el-table>

          <el-table :data="listPromotion" style="margin-top: 2rem" v-bind="getTableConfig">
            <el-table-column align="center" label="升贴" prop="baseName"></el-table-column>
            <el-table-column align="center" label="品名" prop="coalName">
              <template #default="{row}">
                {{ getCurrentCoalName }}
              </template>
            </el-table-column>

            <el-table-column v-for="col in tableColsPromotion" :key="col.prop" :label="col.label"
                             :prop="col.prop" align="center">
              <template #default="{row}">
                <el-input v-model="row[col.prop]" v-focus :placeholder="`请输入${col.label}`"
                          clearable></el-input>
              </template>
            </el-table-column>
          </el-table>
        </SnFormCard>

        <SnFormCard style="margin-top: 3rem" titlePrefixIcon="">
          <template #title>
            <i class="el-icon-s-tools"></i>
            <span style="margin:0 20px 0 7px">配置数据</span>
          </template>

          <el-table :data="list" :span-method="spanMethod" height="35vh" v-bind="getTableConfig">
            <el-table-column align="center" label="日期" prop="date" width="100"></el-table-column>
            <el-table-column align="center" label="基准" prop="baseName"></el-table-column>
            <el-table-column align="center" label="品名" min-width="100"
                             prop="coalName"></el-table-column>
            <el-table-column align="center" label="灰分Ad%" prop="cleanAd"
                             width="120"></el-table-column>
            <el-table-column align="center" label="硫分St,d%" prop="std"
                             width="100"></el-table-column>
            <el-table-column align="center" label="粘结G" prop="procG"></el-table-column>
            <el-table-column align="center" label="胶质层厚度Y/mm" prop="procY"
                             width="140"></el-table-column>
            <el-table-column align="center" label="CSR" prop="csr"></el-table-column>
            <el-table-column align="center" label="升贴" prop="promotion"></el-table-column>
            <el-table-column align="center" label="灰分Ad%" prop="adPromotion"
                             width="120"></el-table-column>
            <el-table-column align="center" label="硫分St,d%" prop="stdPromotion"
                             width="100"></el-table-column>
            <el-table-column align="center" label="粘结G" prop="procGPromotion"></el-table-column>
            <el-table-column align="center" label="胶质层厚度Y/mm" prop="procYPromotion"
                             width="140"></el-table-column>
            <el-table-column align="center" label="CSR" prop="csrPromotion"></el-table-column>
          </el-table>
        </SnFormCard>
      </div>
    </div>
  </div>
</template>

<script>
import {FetchData} from '@/utils'
import {tree} from '@/views/base/CoalCategory/api'
import {list} from '@/views/base/CoalName/api'
import * as ApiParams from './api'
import {formatToDateTime, getDate} from '@/utils/dateUtils'
import {getDictDataLabel} from '@/utils/dict'
import SnSimpleSelect from '@/components/Common/SnSimpleSelect/index.vue'
import SnFormCard from '@/components/Common/SnFormCard/index.vue'

export default {
  name: 'CPParams',
  components: {SnFormCard, SnSimpleSelect},
  data() {
    return {
      coalCategoryId: '',
      coalCategoryList: [],
      coalCategoryProps: {
        label: 'analysisCategoryName',
        value: 'id'
      },
      editKey: '_edit',
      editFlagKey: '_editFlag',
      tableColsBase: [
        {label: '基准', prop: 'baseName'},
        {label: '品名', prop: 'coalName'},
        {label: '灰分Ad%', prop: 'cleanAd'},
        {label: '硫分St,d%', prop: 'std'},
        {label: '粘结G', prop: 'procG'},
        {label: '胶质层厚度Y/mm', prop: 'procY'},
        {label: 'CSR', prop: 'csr'}
      ],
      tableColsPromotion: [
        {label: '灰分Ad%', prop: 'cleanAd'},
        {label: '硫分St,d%', prop: 'std'},
        {label: '粘结G', prop: 'procG'},
        {label: '胶质层厚度Y/mm', prop: 'procY'},
        {label: 'CSR', prop: 'csr'}
      ],
      listBase: [],
      listPromotion: [],
      list: [],
      loading: false,
      coalNameList: [],
      coalNameProps: {
        label: 'coalName',
        value: 'coalName',
        key: 'id'
      }
    }
  },

  mounted() {
    this.getCoalCategoryList()
  },
  directives: {
    focus: {
      inserted: (el) => {
        setTimeout(() => {
          el.children[0].focus()
        }, 10)
      }
    }
  },
  watch: {
    async coalCategoryId(value) {
      this.coalNameList = await FetchData(
        list,
        {
          analysisCategoryName: this.getCurrentCoalCategory[this.coalCategoryProps.label]
        },
        []
      )
      this.getList()
    }
  },
  computed: {
    getCurrentCoalName() {
      return this.listBase[0]?.coalName
    },
    getCurrentCoalCategory() {
      return this.coalCategoryList.find((item) => item[this.coalCategoryProps.value] === this.coalCategoryId)
    },
    getTableConfig() {
      return {
        border: true,
        headerCellStyle: {
          height: '50px',
          background: '#F4F8FF',
          color: '#2C2C2C',
          padding: '0',
          fontSize: '14px',
          fontWeight: '400'
        },
        rowStyle: {
          minHeight: '40px',
          height: '45px',
          fontSize: '14px',
          background: '#fff'
        }
      }
    }
  },
  methods: {
    getDictDataLabel,

    spanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex <= 7) {
        return {
          rowspan: row.rowSpan,
          colspan: 1
        }
      }
      return {rowspan: 1, colspan: 1}
    },
    async handleNameChange(row, col, val, tar) {
      col.type = tar.type
      col.coalName = tar.coalName
      let defaultValue = [{
        cleanAd: '',
        cleanStd: '',
        procG: '',
        csr: '',
        procY: ''
      }]
      if (val) {
        defaultValue = await FetchData(ApiParams.coalNameIndex, {
          date: formatToDateTime(),
          coalName: col.coalName,
          type: col.type
        }, defaultValue)
      }
      col.cleanAd = defaultValue[0].ad
      col.std = defaultValue[0].std
      col.procG = defaultValue[0].g
      col.procY = defaultValue[0].y
      col.csr = defaultValue[0].csr
    },
    async getList() {
      try {
        const {data: sourceList} = await ApiParams.listByCoalCategory({
          // coalCategoryId: this.coalCategoryId,
          coalCategoryName: this.getCurrentCoalCategory.analysisCategoryName,
          orderBy: 'date',
          orderDir: 'desc'
          // type: 'all'
        })

        const config = sourceList[0] || {
          baseName: '',
          coalName: '',
          coalCode: '',
          coalShortname: '',
          cleanAd: '',
          cleanAdRate: '',
          cleanAdValue: '',
          std: '',
          stdRate: '',
          stdValue: '',
          procG: '',
          procGRate: '',
          procGValue: '',
          procY: '',
          procYRate: '',
          procYValue: '',
          csr: '',
          csrRate: '',
          csrValue: ''
        }

        // 基准值列表
        this.listBase = [
          {
            baseName: config.baseName || '基准值',
            coalName: config.coalName,
            coalShortname: config.coalShortname,
            coalCode: config.coalCode,
            cleanAd: config.cleanAd,
            std: config.std,
            procG: config.procG,
            procY: config.procY,
            csr: config.csr
          }
        ]

        // 升贴配置列表
        this.listPromotion = [
          {
            baseName: '升贴标准',
            cleanAd: config.cleanAdRate,
            std: config.stdRate,
            procG: config.procGRate,
            procY: config.procYRate,
            csr: config.csrRate
          },
          {
            baseName: '升贴数值',
            cleanAd: config.cleanAdValue,
            std: config.stdValue,
            procG: config.procGValue,
            procY: config.procYValue,
            csr: config.csrValue
          }
        ]

        // 配置数据列表
        const list = []
        sourceList.forEach((item, index) => {
          const pub = {
            ...item,
            date: item.date ? item.date.substring(0, 10) : ''
          }

          const rate = {
            ...pub,
            promotion: '升贴标准',
            adPromotion: item.cleanAdRate,
            stdPromotion: item.stdRate,
            procGPromotion: item.procGRate,
            procYPromotion: item.procYRate,
            csrPromotion: item.csrRate,
            rowSpan: 2
          }

          const values = {
            ...pub,
            promotion: '升贴数值',
            adPromotion: item.cleanAdValue,
            stdPromotion: item.stdValue,
            procGPromotion: item.procGValue,
            procYPromotion: item.procYValue,
            csrPromotion: item.csrValue,
            rowSpan: 0
          }
          list.push(rate, values)
        })

        this.list = list
      } catch (e) {
        console.log(e, 'e')
      }
    },

    async getCoalCategoryList() {
      const list = await FetchData(tree, undefined, [])
      this.coalCategoryList = list
      if (list.length) {
        this.coalCategoryId = list[0][this.coalCategoryProps.value]
      }
    },

    async handleUpdate() {
      if (this.loading) return
      this.loading = true
      try {
        const getSubmitData = () => {
          const base = this.listBase[0]
          const [rate, value] = this.listPromotion
          return {
            date: formatToDateTime(),
            coalCategoryId: this.coalCategoryId,
            coalCategoryName: this.getCurrentCoalCategory[this.coalCategoryProps.label],
            baseName: base.baseName,
            coalName: base.coalName,
            coalCode: base.coalCode,
            coalShortname: base.coalShortname,
            // 基准值
            cleanAd: base.cleanAd,
            std: base.std,
            procG: base.procG,
            procY: base.procY,
            csr: base.csr,
            // 升贴标准
            cleanAdRate: rate.cleanAd,
            stdRate: rate.std,
            procGRate: rate.procG,
            procYRate: rate.procY,
            csrRate: rate.csr,
            // 升贴数值
            cleanAdValue: value.cleanAd,
            stdValue: value.std,
            procGValue: value.procG,
            procYValue: value.procY,
            csrValue: value.csr,
            type: value.type
          }
        }
        await ApiParams.save(getSubmitData())
        this.$message.success('更新成功')
        this.getList()
      } catch (e) {
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input__inner {
    border: none;
  }
}

.common--page-content {
  padding: 1.25rem 1rem;

  .table {
    //margin-bottom: 1rem;
  }
}
</style>
