// eslint-disable-next-line

import BaseModel, {TableConfig, FormConfig} from '@/components/Common/SnProTable/model'
import {dateUtil, formatToDateTime} from '@/utils/dateUtils'
import fetch from '@/utils/request'

export const name = `/a/coalSourceAll`

const createFormConfig = () => {
  return {
    // fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']],
    filters: [
      {
        label: '日期',
        prop: '_dateRange',
        component: 'SnDateSelect',
        // defaultValue: formatToDateTime(dateUtil().subtract(1, 'days')), // 昨天
        componentProp: {
          clearable: true
        }
      },
      {
        label: '品名简称',
        prop: 'coalName',
        labelWidth: 40
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    mountedQuery: false,
    showOpt: false, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    useApiList: true,
    columns: [
      {
        prop: 'date',
        label: '更新日期',
        slot: 'date',
        format: ({value}) => value.substring(0, 10),
        width: 130
      },
      {
        label: '地区',
        prop: 'location',
        format: ({value}) => {
          return value || '/'
        },
        width: 100
      },
      {
        label: '品名简称',
        prop: 'coalName',
        width: 100
      },
      // {
      //   label: '煤源类型',
      //   prop: 'coalSource',
      //   format: 'dict|b_coal_source_type',
      //   width: 90
      // },
      {
        label: '灰分%',
        prop: 'ad',
        width: 70
      },
      {
        label: '挥发分Vdaf%',
        prop: 'vdaf',
        width: 95
      },
      {
        label: '硫分Std%',
        prop: 'std',
        width: 80
      },
      {
        label: '粘结G',
        prop: 'g',
        width: 70
      },
      {
        label: '胶质层厚度Y/mm',
        prop: 'y',
        width: 120
      },
      {
        label: '煤焦比',
        prop: 'ccr',
        width: 70
      },
      {
        label: 'CSR',
        prop: 'csr',
        width: 70,
        exportWidth: 10
      },
      {
        label: '水分',
        prop: 'mt',
        width: 70,
        exportWidth: 10
      },
      {
        label: '含税煤价',
        prop: 'activePriceCoalPriceWithTax',
        width: 70,
        exportWidth: 10
      },
      {
        label: '运费',
        prop: 'activePriceTransportPriceNoTax',
        width: 70,
        exportWidth: 10
      },
      {
        label: '路耗',
        prop: 'activePriceTransportCostP1',
        width: 70,
        exportWidth: 10
      },
      {
        label: '干基含税成本',
        prop: 'dryBasiscostWithTax',
        width: 110
      },
      {
        label: '吨焦成本',
        prop: 'cokeCost',
        width: 70,
        exportWidth: 10
      },
      {
        label: '建议定价',
        prop: 'performanceCalcPrice',
        width: 90
      },
      {
        label: '和基准值差值',
        prop: 'performanceBaseDiff',
        width: 100
      },
      {
        label: '性价比排序',
        prop: 'performanceSort',
        width: 100
      },
      {
        label: '煤质评价',
        prop: 'remarks',
        minWidth: 150
      }
    ]
  }
}

export class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  list(searchForm) {
    return fetch({
      url: '/cwe/a/coalDatabaseAll/coalDatabaseList',
      method: 'get',
      params: {...searchForm, coalSource: 'zy', type: 'wb'}
    })
  }
}

export default new Model()
