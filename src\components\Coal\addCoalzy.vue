<template>

  <el-dialog class="add" :title="details.name || '新增'" width="90%" top="3vh" :visible.sync="visible"
             :append-to-body="isSourceWash" :before-close="()=>handleClose('full')">
    <div class="coal">
      <el-form ref="entityForm" :model="entityForm" label-width="80px" :disabled="dialogStatus==='watch'">
        <indicators-card>
          <template #title>
            <div class="title-content">
              <span>基础信息</span>
            </div>
          </template>
          <el-row>
            <el-col :span="6">
              <el-form-item label="品名" prop="name">
                <el-input v-model="entityForm.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="矿井名称" prop="mineName">
                <el-input v-model="entityForm.mineName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="库存量" prop="stock">
                <el-input v-model="entityForm.stock"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="price" label="库存价">
                <el-input :value="entityForm.price" type="number"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="指标">
          <el-row>
            <el-col :span="6">
              <el-form-item label="水分" prop="mt">
                <el-input v-model="entityForm.mt" type="number"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="灰分" prop="ad">
                <el-input v-model="entityForm.ad" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="硫分" prop="std">
                <el-input v-model="entityForm.std" type="number"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="挥发分" prop="vdaf">
                <el-input v-model="entityForm.vdaf" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="G" prop="g">
                <el-input v-model="entityForm.g" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="Y" prop="y">
                <el-input v-model="entityForm.y" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="CRC" prop="crc">
                <el-input v-model="entityForm.crc" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="反射率" prop="macR0">
                <el-input v-model="entityForm.macR0" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="macS" label="标准差">
                <el-input v-model="entityForm.macS" type="number"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="recovery" label="回收">
                <el-input v-model="entityForm.recovery" type="number"> </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="精煤指标" :subtitle="`(当前反射率之和为${entityForm.rate>0?entityForm.rate:'空'}，反射率要求在99.5到100.5之间)`">
          <el-row>
            <el-col :span="6" v-for="item in entityForm.rangeDtoList" :key="item.sort">
              <el-form-item :label="item.rangeName">
                <el-input v-model="item.proportion" type="number" @input="changeRate">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

        </indicators-card>
        <indicators-card v-if="details.id" title="图表信息">
          <template v-if="entityForm && entityForm.coalTypeProportionList.length">
            <new-column-chart :impChartData="entityForm" isShowLabel style="width: 100%; height: 300px;" />
            <el-table :data="entityForm.coalTypeProportionList" border :show-header="false" class="chart-table">
              <el-table-column prop="brownCoal" />
              <el-table-column prop="longFlame" />
              <el-table-column prop="gasCoal" />
              <el-table-column prop="thirdCokingCoal" />
              <el-table-column prop="fatCoal" />
              <el-table-column prop="cokingCoal" />
              <el-table-column prop="leanCoal" />
              <el-table-column prop="meagerLeanCoal" />
              <el-table-column prop="meagerCoal" />
              <el-table-column prop="smokelessCoal" />
            </el-table>
          </template>
        </indicators-card>
        <indicators-card v-if="details.id" title="备注">
          <el-form-item prop="remarks" class="textarea">
            <el-input v-model="entityForm.remarks" type="textarea" :rows="6" placeholder="请填写备注信息~">
            </el-input>
          </el-form-item>
        </indicators-card>
      </el-form>
      <el-dialog append-to-body title="煤种不一致" :visible.sync="isChangeCoal" width="80%" :before-close="handleClose">
        <div style="display: flex;font-size: 14px;">
          当前您选择的煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ entityForm.type }}</span>
          ,系统判断煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ type }}</span>
          ,是否修改煤种?
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isChangeCoal = false">取 消</el-button>
          <el-button class="dialog-footer-btns" @click="makeSure('')" type="primary" :loading="entityFormLoading">直接保存</el-button>
          <el-button class="dialog-footer-btns" style="width: 126px;" type="primary" @click="makeSure(type)"
                     :loading="entityFormLoading">确定修改并保存</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- <div slot="footer" class="dialog-footer">
      <el-button class="dialog-footer-btns" type="primary" @click="save" style="width:60px">保存</el-button>
    </div> -->
  </el-dialog>

</template>

<script>
import Func from '@/utils/func'
import { dateFormat } from '@/filters'
import NewColumnChart from '@/components/Chart/NewColumnChart'
import { IndicatorsCard, IndicatorsTable } from '@/components/Indicators/index'
import { getDetail, coalAshList, isCheckCoal, saveCoal, getRangeListApi } from '@/api/coal'

const entityForm = {
  id: '',
  userId: '',
  name: '',
  batchCode: dateFormat(new Date(), 'yyyymmdd'),
  type: '',
  province: '',
  factoryPrice: '',
  transitFee: '',
  roadCost: '',
  arrivePrice: '',
  arriveFactory: 'JT',
  mineDepth: '',
  rawMt: '',
  rawAd: '',
  rawPointFive: '',
  rawOnePointFour: '',
  rawAdIn: '',
  rawStd: '',
  rawVdaf: '',
  rawG: '',
  cleanVdaf: '',
  cleanVd: '',
  cleanAd: '',
  cleanStd: '',
  cleanMt: '',
  cleanP: '',
  cleanMci: '',
  procG: '',
  procY: '',
  procX: '',
  procMf: '',
  procTp: '',
  procTmax: '',
  procTk: '',
  procCrc: '',
  procA: '',
  procB: '',
  macR0: '',
  macS: '',
  macV: '',
  macI: '',
  macE: '',
  comSiO2: '',
  comAl2O3: '',
  comFe2O3: '',
  comCaO: '',
  comMgO: '',
  comNa2O: '',
  comK2O: '',
  comTiO2: '',
  comP2O5: '',
  comSO3: '',
  cfeCp: '',
  cfeCe: '',
  qualScon: '',
  qualPcon: '',
  qualM40: '',
  qualM10: '',
  qualCsr: '',
  qualCri: '',
  qualTestCond: '',
  crGk: '',
  crBk: '',
  crTk: '',
  city: '',
  dataType: '',
  createBy: '',
  createDate: '',
  updateBy: '',
  updateDate: '',
  remarks: '',
  ext: '',
  location: '',
  area: '',
  longitude: '',
  latitude: '',
  isFavorite: '-',
  rawPrice: '',
  coalTypeProportionList: []
}
export default {
  name: 'coal',
  components: {
    IndicatorsCard,
    IndicatorsTable,
    NewColumnChart
  },
  data() {
    return {
      entityForm: { ...entityForm },
      entityFormLoading: false,
      isChangeCoal: false,
      type: '',
      visible: false,
      dialogStatus: 'watch'
    }
  },
  computed: {
    region: {
      set(val) {
        this.entityForm = Object.assign(this.entityForm, {
          province: val[0],
          city: val[1],
          area: val[2]
        })
      },
      get() {
        return [this.entityForm.province, this.entityForm.city, this.entityForm.area]
      }
    }
  },
  props: {
    details: {
      type: Object,
      default() {
        return {}
      }
    },
    addVisible: {
      type: Boolean,
      default: false
    },
    isSourceWash: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    async getRangerList() {
      const res = await Func.fetch(getRangeListApi)
      let rangeDtoList = []
      res.data.forEach((item) => {
        let range = {
          rangeName: '',
          proportion: '',
          sort: '',
          begin: '',
          end: ''
        }
        range.rangeName = item.rangeName
        range.sort = item.sort
        range.proportion = item.proportion
        range.begin = item.begin
        range.end = item.end
        rangeDtoList.push(range)
      })
      this.entityForm = { ...entityForm, rangeDtoList }
    },
    // 保存数据
    async save() {
      const valid = await this.$refs.entityForm.validate()
      if (valid) {
        const res = await Func.fetch(isCheckCoal, {
          g: this.entityForm.procG,
          vdaf: this.entityForm.cleanVdaf,
          y: this.entityForm.procY
        })
        if (res.data) {
          if (res.data.name !== this.entityForm.type) {
            this.isChangeCoal = true
            this.type = res.data.name
          } else {
            this.entityFormLoading = true
            const entityFormData = { ...this.entityForm, dataType: this.datatype }
            const saveRes = await Func.fetch(saveCoal, entityFormData)
            this.entityFormLoading = false
            if (saveRes.data) {
              this.$message({ showClose: true, message: '提交成功', type: 'success' })
              this.closeDialog('full', true)
              // this.handleClose('full')
            }
          }
        }
      }
    },
    async makeSure(coalType) {
      const { arriveFactory } = this.$refs.entityForm
      this.entityForm.type = coalType || this.entityForm.type
      this.entityFormLoading = true
      const entityFormData = { ...this.entityForm, dataType: this.datatype, arriveFactory }
      const saveRes = await Func.fetch(saveCoal, entityFormData)
      this.entityFormLoading = false
      if (saveRes.data) {
        this.$message({ showClose: true, message: '提交成功', type: 'success' })
        // this.$router.back()
        this.closeDialog('full', true)
      }
    },
    closeDialog(type = false) {
      this.dialogFormVisible = false
      this.chartVisible = false
      this.entityForm = { ...entityForm }
      this.isChangeCoal = false
      this.handleClose('full', type)
      // this.$refs.entityForm.$children[0].resetFields()
    },
    async getEntityForm() {
      await this.$nextTick()
      const { id } = this.details
      try {
        const res = await getDetail({ id })
        if (res.data) {
          let rate = 0
          res.data.rangeDtoList.map((item) => {
            if (item.proportion) {
              rate = Number(item.proportion) + rate
            }
          })
          rate = rate.toFixed(1)
          this.entityForm = {
            ...res.data,
            rate
          }
          console.log(this.entityForm)
        }
      } catch (error) {}
    },

    handleActive(e) {
      // this.entityForm.activeInertiaRatio = (e / (100 - e)).toFixed(2)
      this.entityForm = { ...this.entityForm, activeInertiaRatio: (e / (100 - e)).toFixed(2) }
    },

    handleVdaf(e) {
      if (this.entityForm.cleanAd) {
        this.entityForm = { ...this.entityForm, cleanVd: ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2) }
        // this.entityForm.cleanVd = ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2)
      } else {
        this.entityForm = { ...this.entityForm, cleanVd: 0 }
        // this.entityForm.cleanVd = 0
      }
    },

    handleAd(e) {
      if (this.entityForm.cleanVdaf) {
        // this.entityForm.cleanVd = ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2)
        this.entityForm = { ...this.entityForm, cleanVd: ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2) }
      } else {
        // this.entityForm.cleanVd = this.entityForm.cleanVdaf
        this.entityForm = { ...this.entityForm, cleanVd: 0 }
      }
    },
    handleVd(e) {
      if (this.entityForm.cleanAd) {
        // this.entityForm.cleanVdaf = ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2)
        this.entityForm = { ...this.entityForm, cleanVdaf: ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2) }
      } else {
        // this.entityForm.cleanVdaf = e
        this.entityForm = { ...this.entityForm, cleanVdaf: 0 }
      }
    },
    changeRate() {
      this.entityForm.rate = 0
      this.entityForm.coalRockList.map((item) => {
        if (item.proportion) {
          this.entityForm.rate = Number(item.proportion) + this.entityForm.rate
        }
      })
    },
    /*
     * 数值范围验证器
     * */
    RangerValidate(rules, value, cb) {
      if (+value < 0) {
        cb(new Error('数值不能小于0'))
      } else if (+value > 100) {
        cb(new Error('数值不能大于100'))
      } else {
        cb()
      }
    },
    handleClose(type = '', isSave = false) {
      if (type === 'full') {
        this.dialogFormVisible = false
        this.chartVisible = false
        this.entityForm = { ...entityForm }
        this.isChangeCoal = false
        this.$emit('closeVisible', { isSave: isSave })
      } else {
        this.dialogVisible = false
        this.position = {}
        this.isChangeCoal = false
      }
    },
    handleSubmitPosition() {
      if (Object.keys(this.position).length === 0) {
        return
      }
      this.$set(this.entityForm, 'longitude', this.position.lng)
      this.$set(this.entityForm, 'latitude', this.position.lat)
      this.handleClose()
    },
    /**
     * 获取煤灰成份
     */

    async handleCoalAsh() {
      const location = { filter_EQS_area: this.entityForm.area }
      const res = await Func.fetch(coalAshList, location)
      if (res.data.records.length > 0) {
        delete res.data.records[0].id
        // this.$emit('update:entityForm', { ...this.entityForm, ...res.data.records[0] })
        this.entityForm = { ...this.entityForm, ...res.data.records[0] }
      } else {
        this.$message({
          showClose: true,
          message: '无灰成分参考数据，请化验',
          type: 'error'
        })
      }
    },
    /**
     * 关闭煤灰成分弹框
     * @param done
     */
    handleCoalClose(done) {
      done()
    }
  },
  watch: {
    addVisible(v) {
      this.visible = v
    },
    'details.id'(v) {
      if (v) {
        this.getEntityForm()
      } else {
        this.getRangerList()
      }
    },

    details: {
      handler(value) {
        // console.log(value, 'v')
      },
      deep: true,
      immediate: true
    }
  }
}
</script>
<style scoped>
.add >>> .v-modal {
  z-index: 2000 !important;
}
</style>
<style scoped lang='scss' >
@import '@/styles/router-page.scss';

.add {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 80vh;
  }
}

::v-deep .el-form-item__label {
  font-weight: 400;
}

::v-deep .indicators .title {
  display: block;
}

.textarea {
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title_ad {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-col-6 {
  display: flex;
  justify-content: flex-start;
}

.saveBtn {
  display: flex;
  justify-content: flex-end;
}

.dictSelected {
  max-width: 100%;

  ::v-deep .el-input__inner {
    max-width: 100%;
  }
}

.chart-table {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 10px;
  // display: flex;
  // justify-content: center;
}
</style>
